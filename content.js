/**
 * ClipPie - Content Script
 * Handles overlay creation, display, and interaction on web pages
 */

// Global variables
let overlayContainer = null;
let isOverlayVisible = false;
let clipboardHistory = [];
let draggedElement = null;
let stickyNote = null;
let lastFocusedElement = null;
let dragCursor = null;
let stickyNoteWasVisible = false;
let targetElementForPasting = null; // Store the element where user wants to paste
let targetCursorPosition = null; // Store cursor position when overlay opens

// Overlay HTML template
const OVERLAY_HTML = `
<div id="clipboard-companion-overlay" class="cc-overlay">
    <div class="cc-overlay-backdrop"></div>
    <div class="cc-overlay-content">
        <div class="cc-header">
            <h2 class="cc-title">📋 ClipPie</h2>
            <button class="cc-refresh-btn" title="Refresh">🔄</button>
            <button class="cc-close-btn" title="Close (Esc)">×</button>
        </div>
        <div class="cc-tag-cloud" id="cc-tag-cloud">
            <div class="cc-empty-state">
                <div class="cc-empty-icon">📋</div>
                <p>No clipboard items yet</p>
                <small>Copy some text to get started!</small>
            </div>
        </div>
    </div>
    <div class="cc-sticky-note" id="cc-sticky-note"></div>
    <div class="cc-drag-cursor" id="cc-drag-cursor"></div>
</div>
`;

// Sticky note colors (pastel theme)
const STICKY_COLORS = [
    '#FFF9C4', // Yellow
    '#F8BBD0', // Pink
    '#B3E5FC', // Blue
    '#C8E6C9', // Green
    '#FFE0B2', // Orange
    '#E1BEE7', // Purple
    '#FFCDD2', // Light Red
    '#DCEDC8'  // Light Green
];

/**
 * Initialize drag cursor element
 */
function initializeDragCursor() {
    if (!dragCursor) {
        dragCursor = document.createElement('div');
        dragCursor.id = 'cc-drag-cursor';
        dragCursor.className = 'cc-drag-cursor';
        dragCursor.innerHTML = '|';
        dragCursor.style.display = 'none';
        document.body.appendChild(dragCursor);
    }
}

/**
 * Show drag cursor at mouse position
 */
function showDragCursor(x, y) {
    if (dragCursor) {
        dragCursor.style.display = 'block';
        dragCursor.style.left = x + 'px';
        dragCursor.style.top = y + 'px';
    }
}

/**
 * Hide drag cursor
 */
function hideDragCursor() {
    if (dragCursor) {
        dragCursor.style.display = 'none';
    }
}

/**
 * Initialize content script
 */
function init() {
    // Prevent multiple initialization
    if (window.clipboardCompanionInitialized) {
        console.log('ClipPie already initialized');
        return;
    }

    console.log('ClipPie content script loaded');
    window.clipboardCompanionInitialized = true;

    // Load CSS if not already loaded
    loadOverlayCSS();

    // Listen for messages from background script
    chrome.runtime.onMessage.addListener(handleMessage);

    // Setup keyboard listeners
    setupKeyboardListeners();

    // Setup clipboard monitoring
    setupClipboardMonitoring();

    // Track focused elements
    setupFocusTracking();

    // Initialize drag cursor
    initializeDragCursor();
}

/**
 * Load overlay CSS dynamically
 */
function loadOverlayCSS() {
    if (document.getElementById('clipboard-companion-css')) {
        return; // Already loaded
    }
    
    const link = document.createElement('link');
    link.id = 'clipboard-companion-css';
    link.rel = 'stylesheet';
    link.href = chrome.runtime.getURL('overlay.css');
    document.head.appendChild(link);
}

/**
 * Handle messages from background script
 */
function handleMessage(message, _sender, sendResponse) {
    switch (message.action) {
        case 'ping':
            sendResponse({ status: 'alive' });
            break;

        case 'showOverlay':
            showOverlay();
            sendResponse({ success: true });
            break;

        case 'toggleOverlay':
            toggleOverlay();
            sendResponse({ success: true });
            break;

        case 'hideOverlay':
            hideOverlay();
            sendResponse({ success: true });
            break;

        case 'refreshOverlay':
            // Refresh overlay content if it's currently visible
            if (isOverlayVisible) {
                refreshOverlayContent();
            }
            sendResponse({ success: true });
            break;
    }
}

/**
 * Setup keyboard event listeners
 */
function setupKeyboardListeners() {
    document.addEventListener('keydown', (event) => {
        // Close overlay on Escape key
        if (event.key === 'Escape' && isOverlayVisible) {
            hideOverlay();
        }
    });
}

/**
 * Show the clipboard overlay
 */
async function showOverlay() {
    if (isOverlayVisible) {
        return;
    }

    try {
        // Load clipboard history
        await loadClipboardHistory();

        // Create overlay if it doesn't exist
        if (!overlayContainer) {
            createOverlay();
        }

        // Populate with clipboard items
        populateTagCloud();

        // Show overlay
        overlayContainer.style.display = 'flex';
        isOverlayVisible = true;

        // Animate in
        setTimeout(() => {
            overlayContainer.classList.add('cc-visible');
        }, 10);

        console.log('Overlay shown with', clipboardHistory.length, 'items');
        console.log('Target element captured:', targetElementForPasting?.tagName, targetElementForPasting?.type);

    } catch (error) {
        console.error('Error showing overlay:', error);
    }
}

/**
 * Hide the clipboard overlay
 */
function hideOverlay() {
    if (!isOverlayVisible || !overlayContainer) {
        return;
    }

    // Hide sticky note
    hideStickyNote();

    // Animate out
    overlayContainer.classList.remove('cc-visible');

    setTimeout(() => {
        overlayContainer.style.display = 'none';
        isOverlayVisible = false;

        // Clear captured paste target to prevent stale references
        targetElementForPasting = null;
        targetCursorPosition = null;
    }, 300);

    console.log('Overlay hidden');
}

/**
 * Toggle overlay visibility
 */
function toggleOverlay() {
    if (isOverlayVisible) {
        hideOverlay();
    } else {
        showOverlay();
    }
}

/**
 * Refresh overlay content without closing it
 */
async function refreshOverlayContent() {
    if (!isOverlayVisible || !overlayContainer) {
        return;
    }

    try {
        // Reload clipboard history
        await loadClipboardHistory();

        // Repopulate the tag cloud
        populateTagCloud();

        console.log('Overlay content refreshed with', clipboardHistory.length, 'items');
    } catch (error) {
        console.error('Error refreshing overlay content:', error);
    }
}

/**
 * Create the overlay DOM structure
 */
function createOverlay() {
    // Remove existing overlay if any
    const existing = document.getElementById('clipboard-companion-overlay');
    if (existing) {
        existing.remove();
    }
    
    // Create container
    overlayContainer = document.createElement('div');
    overlayContainer.innerHTML = OVERLAY_HTML;
    overlayContainer = overlayContainer.firstElementChild;
    
    // Add to page
    document.body.appendChild(overlayContainer);
    
    // Setup event listeners
    setupOverlayEventListeners();
    
    console.log('Overlay created');
}

/**
 * Setup event listeners for overlay interactions
 */
function setupOverlayEventListeners() {
    // Close button
    const closeBtn = overlayContainer.querySelector('.cc-close-btn');
    closeBtn.tabIndex = -1; // Prevent focus stealing
    closeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        hideOverlay();
    });

    // Refresh button
    const refreshBtn = overlayContainer.querySelector('.cc-refresh-btn');
    refreshBtn.tabIndex = -1; // Prevent focus stealing
    refreshBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        refreshOverlayContent();
    });

    // Prevent content clicks from closing overlay
    const content = overlayContainer.querySelector('.cc-overlay-content');
    content.addEventListener('click', (e) => e.stopPropagation());

    // Make window draggable
    setupWindowDragging();
}

/**
 * Capture the current paste target element and cursor position
 */
function captureCurrentPasteTarget() {
    console.log('=== CAPTURING PASTE TARGET ===');
    console.log('document.activeElement:', document.activeElement?.tagName, document.activeElement?.type, document.activeElement?.id);
    console.log('lastFocusedElement:', lastFocusedElement?.tagName, lastFocusedElement?.type, lastFocusedElement?.id);

    // Priority 1: Currently active element
    const activeElement = document.activeElement;
    if (isEditableElement(activeElement)) {
        targetElementForPasting = activeElement;

        // Capture cursor position for input/textarea elements
        if (activeElement.tagName.toLowerCase() === 'input' || activeElement.tagName.toLowerCase() === 'textarea') {
            targetCursorPosition = {
                start: activeElement.selectionStart || 0,
                end: activeElement.selectionEnd || 0
            };
            console.log('Captured cursor position:', targetCursorPosition);
        } else {
            targetCursorPosition = null;
        }

        console.log('✓ Captured active element as paste target:', activeElement.tagName, activeElement.type, activeElement.id);
        return;
    }

    // Priority 2: Last focused editable element (if still valid and visible)
    if (lastFocusedElement && isEditableElement(lastFocusedElement)) {
        const rect = lastFocusedElement.getBoundingClientRect();
        const isVisible = rect.width > 0 && rect.height > 0 &&
                         lastFocusedElement.offsetParent !== null;

        if (isVisible) {
            targetElementForPasting = lastFocusedElement;
            targetCursorPosition = null; // Don't assume cursor position for unfocused elements
            console.log('✓ Captured last focused element as paste target:', lastFocusedElement.tagName, lastFocusedElement.type, lastFocusedElement.id);
            return;
        }
    }

    // Priority 3: Find the first visible editable element
    const editableElement = findEditableElement();
    if (editableElement) {
        targetElementForPasting = editableElement;
        targetCursorPosition = null;
        console.log('✓ Captured first found editable element as paste target:', editableElement.tagName, editableElement.id);
        return;
    }

    // No suitable target found
    targetElementForPasting = null;
    targetCursorPosition = null;
    console.log('✗ No suitable paste target found');
}

/**
 * Load clipboard history from storage
 */
async function loadClipboardHistory() {
    try {
        const response = await chrome.runtime.sendMessage({ action: 'getClipboardHistory' });
        clipboardHistory = response.history || [];
        console.log('Loaded clipboard history:', clipboardHistory.length, 'items');
    } catch (error) {
        console.error('Error loading clipboard history:', error);
        clipboardHistory = [];
    }
}

/**
 * Populate the tag cloud with clipboard items
 */
function populateTagCloud() {
    const tagCloud = overlayContainer.querySelector('#cc-tag-cloud');
    
    if (clipboardHistory.length === 0) {
        tagCloud.innerHTML = `
            <div class="cc-empty-state">
                <div class="cc-empty-icon">📋</div>
                <p>No clipboard items yet</p>
                <small>Copy some text to get started!</small>
            </div>
        `;
        return;
    }
    
    // Clear existing content
    tagCloud.innerHTML = '';
    
    // Create tags for each clipboard item
    clipboardHistory.forEach((item, index) => {
        const tag = createClipboardTag(item, index);
        tagCloud.appendChild(tag);
    });
    
    console.log('Tag cloud populated with', clipboardHistory.length, 'items');
}

/**
 * Create a clipboard tag element
 */
function createClipboardTag(item, index) {
    const tag = document.createElement('div');
    tag.className = 'cc-tag';
    tag.dataset.itemId = item.id;
    tag.dataset.index = index;

    // Apply pastel color based on item ID
    const colorIndex = parseInt(item.id) % STICKY_COLORS.length;
    const color = STICKY_COLORS[colorIndex];
    tag.style.backgroundColor = color;

    // Truncate content for single line display
    const preview = truncateText(item.content, 20);
    tag.textContent = preview;

    // Add event listeners
    setupTagEventListeners(tag, item);

    return tag;
}

/**
 * Setup window dragging functionality
 */
function setupWindowDragging() {
    const header = overlayContainer.querySelector('.cc-header');
    const content = overlayContainer.querySelector('.cc-overlay-content');
    let isDragging = false;
    let dragOffset = { x: 0, y: 0 };

    header.addEventListener('mousedown', (e) => {
        // Don't start drag if clicking close button
        if (e.target.classList.contains('cc-close-btn')) {
            return;
        }

        isDragging = true;
        content.classList.add('cc-dragging');

        const rect = content.getBoundingClientRect();
        dragOffset.x = e.clientX - rect.left;
        dragOffset.y = e.clientY - rect.top;

        e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;

        const newX = e.clientX - dragOffset.x;
        const newY = e.clientY - dragOffset.y;

        // Keep window within viewport bounds
        const maxX = window.innerWidth - content.offsetWidth;
        const maxY = window.innerHeight - content.offsetHeight;

        const constrainedX = Math.max(0, Math.min(newX, maxX));
        const constrainedY = Math.max(0, Math.min(newY, maxY));

        content.style.left = constrainedX + 'px';
        content.style.top = constrainedY + 'px';
        content.style.margin = '0'; // Remove centering margins

        e.preventDefault();
    });

    document.addEventListener('mouseup', () => {
        if (isDragging) {
            isDragging = false;
            content.classList.remove('cc-dragging');
        }
    });
}

/**
 * Setup event listeners for individual tags
 */
function setupTagEventListeners(tag, item) {

    // Click to paste
    tag.addEventListener('click', (e) => {
        e.stopPropagation();

        // Capture the target right before pasting - use the last focused element
        console.log('Tag clicked, using lastFocusedElement:', lastFocusedElement?.tagName, lastFocusedElement?.type, lastFocusedElement?.id);

        if (lastFocusedElement && isEditableElement(lastFocusedElement)) {
            // Focus the last focused element and paste there
            lastFocusedElement.focus();
            setTimeout(() => {
                insertTextAtCursor(lastFocusedElement, item.content);
            }, 50);
        } else {
            // Fallback to the normal paste logic
            pasteToActiveElement(item.content);
        }

        // Visual feedback - briefly highlight the tag
        const originalBg = tag.style.backgroundColor;
        tag.style.backgroundColor = '#4299e1';
        tag.style.color = 'white';
        tag.style.transform = 'scale(1.05)';

        setTimeout(() => {
            tag.style.backgroundColor = originalBg;
            tag.style.color = '';
            tag.style.transform = '';
        }, 200);

        // Don't auto-close overlay - let user manually close it
        // This allows them to paste multiple items
    });

    // No hover or right-click behavior - keep it simple

    // Drag and drop for clipboard content
    tag.draggable = true;
    tag.addEventListener('dragstart', (e) => {
        draggedElement = tag;
        e.dataTransfer.setData('text/plain', item.content);
        e.dataTransfer.effectAllowed = 'copy';
        tag.classList.add('cc-dragging');

        // Create a custom drag image that's more transparent and positioned below cursor
        const dragImage = tag.cloneNode(true);
        dragImage.style.opacity = '0.4';
        dragImage.style.transform = 'scale(0.8)';
        dragImage.style.position = 'absolute';
        dragImage.style.top = '-1000px';
        document.body.appendChild(dragImage);

        // Set the drag image with offset so it doesn't cover the drop area
        e.dataTransfer.setDragImage(dragImage, 10, 40);

        // Clean up the temporary drag image
        setTimeout(() => {
            document.body.removeChild(dragImage);
        }, 0);

        // Check if sticky note is currently visible
        const stickyNote = overlayContainer?.querySelector('#cc-sticky-note');
        stickyNoteWasVisible = stickyNote && stickyNote.classList.contains('cc-visible');

        // Hide sticky note when dragging starts
        hideStickyNote();

        // Prevent window dragging when dragging tags
        e.stopPropagation();
    });

    tag.addEventListener('dragend', () => {
        tag.classList.remove('cc-dragging');
        draggedElement = null;
        hideDragCursor();

        // Restore sticky note if it was visible before dragging
        if (stickyNoteWasVisible) {
            // Find the item and show sticky note again
            const itemId = tag.dataset.itemId;
            const item = clipboardHistory.find(h => h.id === itemId);
            if (item) {
                setTimeout(() => {
                    showStickyNote({ target: tag }, item);
                }, 100);
            }
        }
        stickyNoteWasVisible = false;
    });
}

/**
 * Show sticky note with full content
 */
function showStickyNote(event, item) {
    hideStickyNote(); // Hide any existing sticky note

    const stickyNote = overlayContainer.querySelector('#cc-sticky-note');
    const colorIndex = parseInt(item.id) % STICKY_COLORS.length;
    const color = STICKY_COLORS[colorIndex];

    // Set content and style
    stickyNote.textContent = item.content;
    stickyNote.style.backgroundColor = color;
    stickyNote.style.display = 'block';

    // Position near the tag
    const rect = event.target.getBoundingClientRect();

    let left = rect.right + 10;
    let top = rect.top;

    // Adjust if sticky note would go off screen
    if (left + 250 > window.innerWidth) {
        left = rect.left - 260;
    }

    if (top + 100 > window.innerHeight) {
        top = window.innerHeight - 110;
    }

    stickyNote.style.left = left + 'px';
    stickyNote.style.top = top + 'px';

    // Store reference to current item for sticky note hover events
    stickyNote.dataset.itemId = item.id;

    // No hover events on sticky note - keep it simple

    // Animate in
    setTimeout(() => {
        stickyNote.classList.add('cc-visible');
    }, 10);
}

/**
 * Clear sticky note timer
 */
function clearAllStickyNoteTimeouts() {
    const stickyNote = overlayContainer?.querySelector('#cc-sticky-note');
    if (stickyNote && stickyNote._hideTimer) {
        clearTimeout(stickyNote._hideTimer);
        stickyNote._hideTimer = null;
    }
}

/**
 * Hide sticky note - SIMPLE
 */
function hideStickyNote() {
    const stickyNote = overlayContainer?.querySelector('#cc-sticky-note');
    if (stickyNote) {
        stickyNote.classList.remove('cc-visible');
        setTimeout(() => {
            stickyNote.style.display = 'none';
        }, 200);
    }
}

/**
 * Paste content to the captured target element or fallback to active element
 */
function pasteToActiveElement(content) {
    console.log('=== ATTEMPTING TO PASTE ===');
    console.log('Content:', content.substring(0, 50) + '...');
    console.log('targetElementForPasting:', targetElementForPasting?.tagName, targetElementForPasting?.type, targetElementForPasting?.id);
    console.log('document.activeElement:', document.activeElement?.tagName, document.activeElement?.type, document.activeElement?.id);

    // Priority 1: Use captured target element from when overlay was opened
    if (targetElementForPasting && isEditableElement(targetElementForPasting)) {
        const rect = targetElementForPasting.getBoundingClientRect();
        const isVisible = rect.width > 0 && rect.height > 0 &&
                         targetElementForPasting.offsetParent !== null;

        if (isVisible) {
            console.log('✓ Using captured target element:', targetElementForPasting.tagName, targetElementForPasting.type, targetElementForPasting.id);

            // Focus the element first
            targetElementForPasting.focus();

            // Restore cursor position if we have it
            if (targetCursorPosition &&
                (targetElementForPasting.tagName.toLowerCase() === 'input' ||
                 targetElementForPasting.tagName.toLowerCase() === 'textarea')) {
                console.log('Restoring cursor position:', targetCursorPosition);
                targetElementForPasting.selectionStart = targetCursorPosition.start;
                targetElementForPasting.selectionEnd = targetCursorPosition.end;
            }

            setTimeout(() => {
                insertTextAtCursor(targetElementForPasting, content);
            }, 50);
            return;
        } else {
            console.log('✗ Captured target element is not visible');
        }
    } else {
        console.log('✗ No valid captured target element');
    }

    // Priority 2: Currently active element (fallback)
    const activeElement = document.activeElement;
    if (isEditableElement(activeElement)) {
        console.log('Pasting to active element:', activeElement.tagName, activeElement.type);
        insertTextAtCursor(activeElement, content);
        return;
    }

    // Priority 3: Last focused editable element (if still valid and visible)
    if (lastFocusedElement && isEditableElement(lastFocusedElement)) {
        const rect = lastFocusedElement.getBoundingClientRect();
        const isVisible = rect.width > 0 && rect.height > 0 &&
                         lastFocusedElement.offsetParent !== null;

        if (isVisible) {
            console.log('Pasting to last focused element:', lastFocusedElement.tagName, lastFocusedElement.type);
            lastFocusedElement.focus();
            setTimeout(() => {
                insertTextAtCursor(lastFocusedElement, content);
            }, 50);
            return;
        }
    }

    // Priority 4: Find the first visible editable element
    const editableElement = findEditableElement();
    if (editableElement) {
        console.log('Pasting to first found editable element:', editableElement.tagName);
        editableElement.focus();
        setTimeout(() => {
            insertTextAtCursor(editableElement, content);
        }, 50);
        return;
    }

    // Fallback: copy to clipboard (no notification)
    console.log('No suitable element found, copying to clipboard');
    copyToClipboard(content);
}

/**
 * Check if element is editable
 */
function isEditableElement(element) {
    if (!element) return false;

    const tagName = element.tagName.toLowerCase();
    const type = element.type ? element.type.toLowerCase() : '';

    // Check for input elements
    const isInput = tagName === 'input' && [
        'text', 'email', 'password', 'search', 'url', 'tel',
        'number', 'date', 'datetime-local', 'month', 'time', 'week'
    ].includes(type);

    // Check for textarea
    const isTextarea = tagName === 'textarea';

    // Check for contenteditable
    const isContentEditable = element.contentEditable === 'true' || element.isContentEditable;

    // Check if element is disabled or readonly
    const isDisabled = element.disabled || element.readOnly;

    return (isInput || isTextarea || isContentEditable) && !isDisabled;
}

/**
 * Find the first editable element on the page
 */
function findEditableElement() {
    const selectors = [
        'input[type="text"]:not([disabled]):not([readonly])',
        'input[type="email"]:not([disabled]):not([readonly])',
        'input[type="password"]:not([disabled]):not([readonly])',
        'input[type="search"]:not([disabled]):not([readonly])',
        'input[type="url"]:not([disabled]):not([readonly])',
        'input[type="tel"]:not([disabled]):not([readonly])',
        'input[type="number"]:not([disabled]):not([readonly])',
        'input:not([type]):not([disabled]):not([readonly])', // Default type is text
        'textarea:not([disabled]):not([readonly])',
        '[contenteditable="true"]'
    ];

    for (const selector of selectors) {
        const elements = document.querySelectorAll(selector);
        for (const element of elements) {
            // Check if element is visible
            const rect = element.getBoundingClientRect();
            const isVisible = rect.width > 0 && rect.height > 0 &&
                             element.offsetParent !== null &&
                             getComputedStyle(element).visibility !== 'hidden' &&
                             getComputedStyle(element).display !== 'none';

            if (isVisible) {
                return element;
            }
        }
    }

    return null;
}

/**
 * Insert text at cursor position
 */
function insertTextAtCursor(element, text) {
    console.log('Inserting text into:', element.tagName, element.type);

    try {
        if (element.tagName.toLowerCase() === 'input' || element.tagName.toLowerCase() === 'textarea') {
            // For input and textarea elements
            const start = element.selectionStart || 0;
            const end = element.selectionEnd || 0;
            const value = element.value || '';

            const newValue = value.substring(0, start) + text + value.substring(end);
            element.value = newValue;

            // Set cursor position after inserted text
            const newCursorPos = start + text.length;
            element.selectionStart = newCursorPos;
            element.selectionEnd = newCursorPos;

            // Trigger events to notify frameworks
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));

            console.log('Text inserted successfully into input/textarea');

        } else if (element.contentEditable === 'true' || element.isContentEditable) {
            // For contenteditable elements
            element.focus();

            const selection = window.getSelection();
            let range;

            if (selection.rangeCount > 0) {
                range = selection.getRangeAt(0);
            } else {
                range = document.createRange();
                range.selectNodeContents(element);
                range.collapse(false);
            }

            range.deleteContents();
            const textNode = document.createTextNode(text);
            range.insertNode(textNode);
            range.setStartAfter(textNode);
            range.collapse(true);

            selection.removeAllRanges();
            selection.addRange(range);

            // Trigger events
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));

            console.log('Text inserted successfully into contenteditable');

        } else {
            console.log('Element not suitable for text insertion');
            copyToClipboard(text);
        }
    } catch (error) {
        console.error('Error inserting text:', error);
        copyToClipboard(text);
    }
}



/**
 * Copy text to clipboard as fallback - DISABLED to prevent permission popups
 */
function copyToClipboard(text) {
    console.log('Clipboard writing disabled to prevent permission popups. Content:', text.substring(0, 50) + '...');
    // No clipboard operations to avoid permission requests
}

/**
 * Truncate text for display
 */
function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength) + '...';
}

/**
 * Setup drag and drop listeners for the entire page
 */
function setupGlobalDragDropListeners() {
    // Track mouse movement during drag to show cursor
    document.addEventListener('dragover', (e) => {
        if (isEditableElement(e.target) && draggedElement) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'copy';

            // Visual feedback on the input field
            e.target.style.backgroundColor = '#e8f4fd';
            e.target.style.borderColor = '#4299e1';

            // Show drag cursor positioned to the left of mouse to avoid being covered by drag image
            showDragCursor(e.clientX - 15, e.clientY - 10);
        } else if (draggedElement) {
            // Hide cursor when not over editable elements
            hideDragCursor();
        }
    });

    // Remove visual feedback when drag leaves
    document.addEventListener('dragleave', (e) => {
        if (isEditableElement(e.target)) {
            e.target.style.backgroundColor = '';
            e.target.style.borderColor = '';
        }
    });

    // Handle drop
    document.addEventListener('drop', (e) => {
        // Remove visual feedback
        if (isEditableElement(e.target)) {
            e.target.style.backgroundColor = '';
            e.target.style.borderColor = '';
        }

        if (isEditableElement(e.target) && draggedElement) {
            e.preventDefault();
            const text = e.dataTransfer.getData('text/plain');
            console.log('Dropping text via drag and drop:', text.substring(0, 50) + '...');
            insertTextAtCursor(e.target, text);

            // Don't hide overlay after successful drop - let user continue using it
            // This allows them to paste multiple items without reopening the overlay
        }

        // Hide drag cursor after drop
        hideDragCursor();
    });
}

/**
 * Setup focus tracking to remember the last focused editable element
 */
function setupFocusTracking() {
    // Track focus on all editable elements
    document.addEventListener('focusin', (e) => {
        if (isEditableElement(e.target)) {
            lastFocusedElement = e.target;
            console.log('✓ Focused element tracked:', e.target.tagName, e.target.type, e.target.id || e.target.className);
        }
    });

    // Track clicks on editable elements (even if they don't change focus)
    document.addEventListener('click', (e) => {
        if (isEditableElement(e.target)) {
            lastFocusedElement = e.target;
            console.log('✓ Clicked element tracked:', e.target.tagName, e.target.type, e.target.id || e.target.className);
        }
    });

    // Don't clear the last focused element too aggressively
    // Only clear it if the user explicitly focuses on something else for a long time
    document.addEventListener('focusout', () => {
        // Keep the last focused element for much longer - only clear if user focuses elsewhere for 30 seconds
        setTimeout(() => {
            if (!isEditableElement(document.activeElement)) {
                setTimeout(() => {
                    if (!isEditableElement(document.activeElement)) {
                        console.log('Clearing lastFocusedElement after long inactivity');
                        lastFocusedElement = null;
                    }
                }, 30000); // Clear after 30 seconds of no editable focus
            }
        }, 100);
    });
}

/**
 * Setup clipboard monitoring - No permission popup approach
 */
function setupClipboardMonitoring() {
    console.log('✓ Clipboard monitoring enabled (permission-free mode)');

    // Detect Google Docs environment
    const isGoogleDocs = window.location.hostname.includes('docs.google.com');
    if (isGoogleDocs) {
        console.log('🔍 Google Docs detected - using enhanced monitoring');
        setupGoogleDocsMonitoring();
    }

    // Listen for copy events and capture selected text
    document.addEventListener('copy', async (event) => {
        console.log('📋 Copy event detected');
        handleCopyEvent(event);
    });

    // Listen for Ctrl+C keyboard shortcut
    document.addEventListener('keydown', async (event) => {
        // Check for Ctrl+C (or Cmd+C on Mac)
        if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'c') {
            console.log('⌨️ Ctrl+C keyboard shortcut detected');
            // Small delay to let the copy operation complete
            setTimeout(() => {
                handleCopyKeyboard(event);
            }, 50);
        }
    });

    // Listen for selection changes to capture potential copy content
    document.addEventListener('selectionchange', () => {
        captureCurrentSelection();
    });
}

/**
 * Handle clipboard capture from copy events or Ctrl+C
 */
async function handleClipboardCapture(source = 'unknown') {
    console.log(`📋 Handling clipboard capture from: ${source}`);

    // DISABLE automatic clipboard reading to prevent permission popups
    // Instead, rely on manual clipboard operations through the popup
    console.log('⚠️ Automatic clipboard reading disabled to prevent permission popups');
    console.log('� Use the extension popup to manually add clipboard content');

    // Don't try to read clipboard automatically - this causes permission popups
    // The user can manually add content through the popup if needed
}

// Initialize when script loads
init();

// Setup global drag and drop
setupGlobalDragDropListeners();
